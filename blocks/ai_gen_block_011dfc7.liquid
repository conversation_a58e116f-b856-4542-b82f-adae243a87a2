{% doc %}
  @prompt
    Create a full-width video hero section that actually plays videos properly. Include proper HTML5 video element with controls, autoplay functionality, loop options, and mute settings. The video should maintain aspect ratio without cropping and include fallback poster image. Add settings for video file upload, autoplay toggle, mute toggle, loop toggle, and video controls visibility. Ensure the video element works correctly with uploaded video files.

{% enddoc %}
{% assign ai_gen_id = block.id | replace: '_', '' | downcase %}

{% style %}
  .ai-video-hero-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    min-height: 100vh;
    overflow: hidden;
    background-color: {{ block.settings.background_color }};
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ai-video-container-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .ai-video-element-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
    z-index: 1;
  }

  .ai-video-poster-{{ ai_gen_id }} {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    object-fit: cover;
    z-index: 1;
  }

  .ai-video-dark-overlay-{{ ai_gen_id }} {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.6) 100%);
    z-index: 2;
    pointer-events: none;
  }

  .ai-video-overlay-{{ ai_gen_id }} {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    box-sizing: border-box;
    z-index: 10;
    text-align: center;
  }

  .ai-video-content-{{ ai_gen_id }} {
    max-width: 800px;
    width: 100%;
    text-align: center;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.3);
    padding: 3rem 2rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
  }

  .ai-video-heading-{{ ai_gen_id }} {
    margin: 0 0 1.5rem 0;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: -0.02em;
    animation: fadeInUp 1s ease-out 0.3s both;
  }

  .ai-video-text-{{ ai_gen_id }} {
    margin: 0 0 2rem 0;
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-weight: 300;
    animation: fadeInUp 1s ease-out 0.6s both;
  }

  .ai-video-button-{{ ai_gen_id }} {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease-out 0.9s both;
  }

  .ai-video-button-{{ ai_gen_id }}::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .ai-video-button-{{ ai_gen_id }}:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .ai-video-button-{{ ai_gen_id }}:hover::before {
    left: 100%;
  }

  /* Animations */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Design */
  @media screen and (max-width: 1024px) {
    .ai-video-hero-{{ ai_gen_id }} {
      min-height: 80vh;
    }

    .ai-video-content-{{ ai_gen_id }} {
      max-width: 90%;
      padding: 2.5rem 1.5rem;
    }
  }

  @media screen and (max-width: 768px) {
    .ai-video-hero-{{ ai_gen_id }} {
      min-height: 70vh;
    }

    .ai-video-overlay-{{ ai_gen_id }} {
      padding: 1rem;
    }

    .ai-video-content-{{ ai_gen_id }} {
      max-width: 95%;
      padding: 2rem 1.5rem;
      border-radius: 15px;
    }

    .ai-video-button-{{ ai_gen_id }} {
      padding: 0.8rem 2rem;
      font-size: 1rem;
    }
  }

  @media screen and (max-width: 480px) {
    .ai-video-hero-{{ ai_gen_id }} {
      min-height: 60vh;
    }

    .ai-video-content-{{ ai_gen_id }} {
      padding: 1.5rem 1rem;
      border-radius: 10px;
    }

    .ai-video-button-{{ ai_gen_id }} {
      padding: 0.7rem 1.5rem;
      font-size: 0.9rem;
    }
  }
{% endstyle %}

<div class="ai-video-hero-{{ ai_gen_id }}" {{ block.shopify_attributes }}>
  <div class="ai-video-container-{{ ai_gen_id }}">
    {% if block.settings.video != blank %}
      <video 
        id="ai-video-{{ ai_gen_id }}"
        class="ai-video-element-{{ ai_gen_id }}"
        {% if block.settings.autoplay %}autoplay{% endif %}
        {% if block.settings.loop %}loop{% endif %}
        {% if block.settings.muted %}muted{% endif %}
        {% if block.settings.show_controls %}controls{% endif %}
        {% if block.settings.poster != blank %}poster="{{ block.settings.poster | image_url: width: 2000 }}"{% endif %}
        playsinline
      >
        <source src="{{ block.settings.video }}" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    {% elsif block.settings.poster != blank %}
      <img 
        src="{{ block.settings.poster | image_url: width: 2000 }}"
        alt="{{ block.settings.poster.alt | escape }}"
        class="ai-video-poster-{{ ai_gen_id }}"
        loading="lazy"
      >
    {% else %}
      <div class="ai-video-poster-{{ ai_gen_id }}">
        {{ 'hero-apparel-1' | placeholder_svg_tag }}
      </div>
    {% endif %}
  </div>

  {% if block.settings.show_video_overlay %}
    <div class="ai-video-dark-overlay-{{ ai_gen_id }}"></div>
  {% endif %}

  {% if block.settings.show_content %}
    <div class="ai-video-overlay-{{ ai_gen_id }}">
      <div class="ai-video-content-{{ ai_gen_id }}">
        {% if block.settings.heading != blank %}
          <h2 class="ai-video-heading-{{ ai_gen_id }}">{{ block.settings.heading }}</h2>
        {% endif %}
        
        {% if block.settings.text != blank %}
          <div class="ai-video-text-{{ ai_gen_id }}">{{ block.settings.text }}</div>
        {% endif %}
        
        {% if block.settings.button_text != blank and block.settings.button_link != blank %}
          <a href="{{ block.settings.button_link }}" class="ai-video-button-{{ ai_gen_id }}">
            {{ block.settings.button_text }}
          </a>
        {% endif %}
      </div>
    </div>
  {% endif %}
</div>

<script>
  (function() {
    const video = document.getElementById('ai-video-{{ ai_gen_id }}');
    
    if (video) {
      // Handle autoplay with muted setting for browsers that require it
      if ({{ block.settings.autoplay | json }} && {{ block.settings.muted | json }}) {
        video.muted = true;
        
        // Try to play the video
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            // Auto-play was prevented, try again with user interaction
            console.log('Autoplay prevented:', error);
          });
        }
      }
      
      // Handle iOS low power mode which may prevent autoplay
      document.addEventListener('touchstart', () => {
        if ({{ block.settings.autoplay | json }} && video.paused) {
          video.play().catch(e => {
            console.log('Play after touch prevented:', e);
          });
        }
      }, { once: true });
    }
  })();
</script>

{% schema %}
{
  "name": "Video Hero",
  "tag": null,
  "settings": [
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "url",
      "id": "video",
      "label": "Video URL"
    },
    {
      "type": "image_picker",
      "id": "poster",
      "label": "Poster image (fallback)"
    },
    {
      "type": "select",
      "id": "aspect_ratio",
      "label": "Aspect ratio",
      "options": [
        {
          "value": "1.78",
          "label": "16:9"
        },
        {
          "value": "1.33",
          "label": "4:3"
        },
        {
          "value": "2.35",
          "label": "2.35:1 (Cinematic)"
        },
        {
          "value": "1",
          "label": "1:1 (Square)"
        },
        {
          "value": "0.8",
          "label": "4:5 (Portrait)"
        },
        {
          "value": "0.56",
          "label": "9:16 (Mobile)"
        }
      ],
      "default": "1.78"
    },
    {
      "type": "select",
      "id": "object_fit",
      "label": "Video fit",
      "options": [
        {
          "value": "cover",
          "label": "Cover (may crop)"
        },
        {
          "value": "contain",
          "label": "Contain (full video)"
        }
      ],
      "default": "cover"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay video",
      "default": true,
      "info": "Autoplay requires the video to be muted on most browsers"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "muted",
      "label": "Mute video",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_controls",
      "label": "Show video controls",
      "default": false
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#000000"
    },
    {
      "type": "checkbox",
      "id": "show_video_overlay",
      "label": "Show dark overlay on video",
      "default": true,
      "info": "Adds a dark overlay to improve text readability"
    },
    {
      "type": "range",
      "id": "video_overlay_opacity",
      "min": 0,
      "max": 80,
      "step": 5,
      "unit": "%",
      "label": "Video overlay opacity",
      "default": 40,
      "info": "Controls the darkness of the overlay"
    },
    {
      "type": "header",
      "content": "Content Overlay"
    },
    {
      "type": "checkbox",
      "id": "show_content",
      "label": "Show content overlay",
      "default": true
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Full-width Video Hero"
    },
    {
      "type": "textarea",
      "id": "text",
      "label": "Text content",
      "default": "Use this section to welcome customers to your store, highlight products, or share brand announcements."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "Shop Now"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button link"
    },
    {
      "type": "select",
      "id": "content_vertical_align",
      "label": "Content vertical alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Top"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Bottom"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "content_horizontal_align",
      "label": "Content horizontal alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "flex-end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "text_align",
      "label": "Text alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "content_width",
      "min": 300,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Content max width",
      "default": 600
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "content_background_color",
      "label": "Content background color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "content_background_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Content background opacity",
      "default": 50
    },
    {
      "type": "range",
      "id": "content_padding",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Content padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "content_border_radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Content border radius",
      "default": 4
    },
    {
      "type": "header",
      "content": "Text Styling"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 80,
      "step": 2,
      "unit": "px",
      "label": "Heading size",
      "default": 40
    },
    {
      "type": "select",
      "id": "heading_weight",
      "label": "Heading weight",
      "options": [
        {
          "value": "400",
          "label": "Regular"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semibold"
        },
        {
          "value": "700",
          "label": "Bold"
        }
      ],
      "default": "600"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 30,
      "step": 1,
      "unit": "px",
      "label": "Text size",
      "default": 16
    },
    {
      "type": "header",
      "content": "Button Styling"
    },
    {
      "type": "color",
      "id": "button_background_color",
      "label": "Button background",
      "default": "#4a90e2"
    },
    {
      "type": "color",
      "id": "button_text_color",
      "label": "Button text",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "button_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Button border radius",
      "default": 4
    }
  ],
  "presets": [
    {
      "name": "Video Hero"
    }
  ]
}
{% endschema %}